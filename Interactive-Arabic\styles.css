* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-brown: #D4A574;
    --light-brown: #E8D5B7;
    --dark-brown: #B8956A;
    --cream: #F5F0E8;
    --warm-white: #FEFCF8;
    --accent-gold: #C9A96E;
    --text-dark: #5D4E37;
    --shadow-light: rgba(212, 165, 116, 0.2);
    --shadow-medium: rgba(212, 165, 116, 0.4);
}

body {
    font-family: 'Inter', sans-serif;
    background: linear-gradient(135deg, var(--warm-white) 0%, var(--cream) 50%, var(--light-brown) 100%);
    min-height: 100vh;
    overflow: hidden;
    position: relative;
}

/* Navbar Styles */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    border-bottom: 1px solid rgba(212, 165, 116, 0.2);
    z-index: 1000;
    padding: 1rem 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.logo-main {
    font-family: 'Amiri', serif;
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-dark);
    text-shadow: 0 1px 2px var(--shadow-light);
    line-height: 1;
    letter-spacing: 1px;
}

.logo-subtitle {
    font-family: 'Inter', sans-serif;
    font-size: 0.7rem;
    font-weight: 500;
    color: var(--dark-brown);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: -2px;
    opacity: 0.8;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-item {
    margin: 0;
}

.nav-link {
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 500;
    font-size: 1rem;
    transition: all 0.3s ease;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    position: relative;
}

.nav-link:hover {
    color: var(--primary-brown);
    background: rgba(212, 165, 116, 0.1);
    transform: translateY(-2px);
}

.container {
    width: 100%;
    height: 100vh;
    position: relative;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    padding-top: 12vh;
}

.background-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 20% 20%, var(--shadow-light) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, var(--shadow-light) 0%, transparent 50%),
        radial-gradient(circle at 40% 70%, var(--shadow-light) 0%, transparent 30%);
    opacity: 0.6;
}

.content {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 900px;
    padding: 2rem;
}

.hero-section {
    position: relative;
    padding: 4rem 2rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 30px;
    border: 1px solid rgba(212, 165, 116, 0.3);
    box-shadow: 
        0 20px 40px var(--shadow-light),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.title {
    font-family: 'Amiri', serif;
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    color: var(--text-dark);
    line-height: 1.2;
    text-shadow: 0 2px 4px var(--shadow-light);
    letter-spacing: -0.02em;
    margin: 0;
    position: relative;
}

.description-text {
    margin-top: 2rem;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

.description-text p {
    font-family: 'Inter', sans-serif;
    font-size: clamp(1rem, 2.5vw, 1.2rem);
    font-weight: 400;
    color: var(--text-dark);
    line-height: 1.6;
    text-align: center;
    margin: 0;
    opacity: 0.9;
    text-shadow: 0 1px 2px var(--shadow-light);
}

/* CTA Section */
.cta-section {
    margin-top: 3rem;
    text-align: center;
}

.auth-button {
    background: linear-gradient(135deg, var(--primary-brown) 0%, var(--accent-gold) 100%);
    color: var(--warm-white);
    border: none;
    padding: 1rem 2.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow:
        0 8px 25px var(--shadow-medium),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    font-family: 'Inter', sans-serif;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.auth-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.auth-button:hover {
    transform: translateY(-3px);
    box-shadow:
        0 12px 35px var(--shadow-medium),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.auth-button:hover::before {
    left: 100%;
}

.auth-button:active {
    transform: translateY(-1px);
}



.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.floating-circle {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(45deg, var(--primary-brown), var(--accent-gold));
    opacity: 0.1;
}

.circle-1 {
    width: 120px;
    height: 120px;
    top: 10%;
    left: 10%;
    animation: drift 20s linear infinite;
}

.circle-2 {
    width: 80px;
    height: 80px;
    top: 70%;
    right: 15%;
    animation: drift 25s linear infinite reverse;
}

.circle-3 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    left: 20%;
    animation: drift 30s linear infinite;
}

.floating-square {
    position: absolute;
    background: linear-gradient(45deg, var(--light-brown), var(--primary-brown));
    opacity: 0.08;
    transform: rotate(45deg);
}

.square-1 {
    width: 60px;
    height: 60px;
    top: 30%;
    right: 10%;
    animation: rotate-drift 35s linear infinite;
}

.square-2 {
    width: 40px;
    height: 40px;
    bottom: 40%;
    right: 30%;
    animation: rotate-drift 40s linear infinite reverse;
}



@keyframes drift {
    0% {
        transform: translateX(-50px) translateY(-50px);
    }
    25% {
        transform: translateX(50px) translateY(-100px);
    }
    50% {
        transform: translateX(100px) translateY(50px);
    }
    75% {
        transform: translateX(-30px) translateY(100px);
    }
    100% {
        transform: translateX(-50px) translateY(-50px);
    }
}

@keyframes rotate-drift {
    0% {
        transform: rotate(45deg) translateX(-30px) translateY(-30px);
    }
    25% {
        transform: rotate(135deg) translateX(30px) translateY(-60px);
    }
    50% {
        transform: rotate(225deg) translateX(60px) translateY(30px);
    }
    75% {
        transform: rotate(315deg) translateX(-20px) translateY(60px);
    }
    100% {
        transform: rotate(405deg) translateX(-30px) translateY(-30px);
    }
}

@media (max-width: 768px) {
    .nav-container {
        padding: 0 1rem;
    }

    .logo-main {
        font-size: 1.5rem;
    }

    .logo-subtitle {
        font-size: 0.6rem;
    }

    .nav-menu {
        gap: 1rem;
    }

    .nav-link {
        font-size: 0.9rem;
        padding: 0.4rem 0.8rem;
    }

    .container {
        padding-top: 10vh;
    }

    .hero-section {
        padding: 3rem 1.5rem;
        margin: 1rem;
    }

    .description-text {
        margin-top: 1.5rem;
        max-width: 100%;
        padding: 0 1rem;
    }

    .description-text p {
        font-size: 0.95rem;
        line-height: 1.5;
    }

    .floating-circle, .floating-square {
        opacity: 0.05;
    }

    .auth-button {
        padding: 0.9rem 2rem;
        font-size: 1rem;
    }
}
