// Supabase configuration and client setup
const SUPABASE_URL = 'https://ognmrnoqvauhlyaqsfza.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9nbW1ybm9xdmF1aGx5YXFzZnphIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEyNTMzNDQsImV4cCI6MjA2NjgyOTM0NH0.jtf4EzqAEf3ydewocoaYD0zNqm7VSSOSdjIXxMqCoC8';

// Initialize Supabase client (will be initialized after CDN loads)
let supabase;

// Authentication functions
class AuthManager {
    constructor() {
        this.currentUser = null;
        // Don't initialize immediately, wait for DOM and Supabase to load
    }

    async init() {
        // Initialize Supabase client if not already done
        if (!supabase) {
            supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        }

    async init() {
        // Check for existing session
        const { data: { session } } = await supabase.auth.getSession();
        if (session) {
            this.currentUser = session.user;
            this.updateUI();
        }

        // Listen for auth changes
        supabase.auth.onAuthStateChange((event, session) => {
            if (event === 'SIGNED_IN') {
                this.currentUser = session.user;
                this.updateUI();
            } else if (event === 'SIGNED_OUT') {
                this.currentUser = null;
                this.updateUI();
            }
        });
    }

    async signUp(email, password, fullName) {
        try {
            const { data, error } = await supabase.auth.signUp({
                email: email,
                password: password,
                options: {
                    data: {
                        full_name: fullName
                    }
                }
            });

            if (error) throw error;
            
            return { success: true, data };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    async signIn(email, password) {
        try {
            const { data, error } = await supabase.auth.signInWithPassword({
                email: email,
                password: password
            });

            if (error) throw error;
            
            return { success: true, data };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    async signOut() {
        try {
            const { error } = await supabase.auth.signOut();
            if (error) throw error;
            
            return { success: true };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    async resetPassword(email) {
        try {
            const { error } = await supabase.auth.resetPasswordForEmail(email);
            if (error) throw error;
            
            return { success: true };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    updateUI() {
        const authButton = document.querySelector('.auth-button');
        const navMenu = document.querySelector('.nav-menu');

        if (this.currentUser) {
            // User is signed in
            authButton.textContent = 'Dashboard';
            authButton.onclick = () => window.location.href = 'dashboard.html';

            // Add logout option to nav
            const logoutItem = document.createElement('li');
            logoutItem.className = 'nav-item';
            logoutItem.innerHTML = `<a href="#" class="nav-link logout-link">Logout</a>`;
            
            // Remove existing logout link if present
            const existingLogout = navMenu.querySelector('.logout-link');
            if (existingLogout) {
                existingLogout.parentElement.remove();
            }
            
            navMenu.appendChild(logoutItem);

            // Add logout functionality
            logoutItem.querySelector('.logout-link').onclick = async (e) => {
                e.preventDefault();
                await this.signOut();
            };

        } else {
            // User is not signed in
            authButton.textContent = 'Sign In / Sign Up';
            authButton.onclick = () => this.showAuthModal();

            // Remove logout link if present
            const logoutLink = navMenu.querySelector('.logout-link');
            if (logoutLink) {
                logoutLink.parentElement.remove();
            }
        }
    }

    showAuthModal() {
        const modal = document.getElementById('authModal');
        if (modal) {
            modal.style.display = 'flex';
        }
    }

    hideAuthModal() {
        const modal = document.getElementById('authModal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    isAuthenticated() {
        return this.currentUser !== null;
    }

    getCurrentUser() {
        return this.currentUser;
    }
}

// Initialize auth manager when DOM is loaded
let authManager;

document.addEventListener('DOMContentLoaded', async function() {
    // Initialize Supabase client
    supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

    // Initialize auth manager
    authManager = new AuthManager();
    await authManager.init();

    // Export for use in other files
    window.authManager = authManager;
    window.supabase = supabase;
});
