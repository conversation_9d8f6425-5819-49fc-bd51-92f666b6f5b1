// Authentication Modal Management
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Supabase client
    supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
    
    // Modal elements
    const modal = document.getElementById('authModal');
    const closeBtn = document.querySelector('.close');
    const authForm = document.getElementById('authForm');
    const modalTitle = document.getElementById('modalTitle');
    const authSubmit = document.getElementById('authSubmit');
    const authSwitchText = document.getElementById('authSwitchText');
    const authSwitchLink = document.getElementById('authSwitchLink');
    const forgotPasswordLink = document.getElementById('forgotPasswordLink');
    const authError = document.getElementById('authError');
    const authSuccess = document.getElementById('authSuccess');
    
    // Form fields
    const nameField = document.getElementById('nameField');
    const confirmPasswordField = document.getElementById('confirmPasswordField');
    const fullNameInput = document.getElementById('fullName');
    const emailInput = document.getElementById('email');
    const passwordInput = document.getElementById('password');
    const confirmPasswordInput = document.getElementById('confirmPassword');
    
    let isSignUpMode = false;
    let isForgotPasswordMode = false;

    // Modal control functions
    function showModal() {
        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
    }

    function hideModal() {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
        resetForm();
    }

    function resetForm() {
        authForm.reset();
        hideMessages();
        setSignInMode();
    }

    function hideMessages() {
        authError.style.display = 'none';
        authSuccess.style.display = 'none';
    }

    function showError(message) {
        authError.textContent = message;
        authError.style.display = 'block';
        authSuccess.style.display = 'none';
    }

    function showSuccess(message) {
        authSuccess.textContent = message;
        authSuccess.style.display = 'block';
        authError.style.display = 'none';
    }

    // Mode switching functions
    function setSignInMode() {
        isSignUpMode = false;
        isForgotPasswordMode = false;
        modalTitle.textContent = 'Sign In';
        authSubmit.textContent = 'Sign In';
        nameField.style.display = 'none';
        confirmPasswordField.style.display = 'none';
        passwordInput.parentElement.style.display = 'block';
        authSwitchText.innerHTML = 'Don\'t have an account? <a href="#" id="authSwitchLink">Sign Up</a>';
        forgotPasswordLink.style.display = 'block';
        
        // Re-attach event listener
        document.getElementById('authSwitchLink').onclick = (e) => {
            e.preventDefault();
            setSignUpMode();
        };
    }

    function setSignUpMode() {
        isSignUpMode = true;
        isForgotPasswordMode = false;
        modalTitle.textContent = 'Sign Up';
        authSubmit.textContent = 'Sign Up';
        nameField.style.display = 'block';
        confirmPasswordField.style.display = 'block';
        passwordInput.parentElement.style.display = 'block';
        authSwitchText.innerHTML = 'Already have an account? <a href="#" id="authSwitchLink">Sign In</a>';
        forgotPasswordLink.style.display = 'none';
        
        // Re-attach event listener
        document.getElementById('authSwitchLink').onclick = (e) => {
            e.preventDefault();
            setSignInMode();
        };
    }

    function setForgotPasswordMode() {
        isForgotPasswordMode = true;
        isSignUpMode = false;
        modalTitle.textContent = 'Reset Password';
        authSubmit.textContent = 'Send Reset Email';
        nameField.style.display = 'none';
        confirmPasswordField.style.display = 'none';
        passwordInput.parentElement.style.display = 'none';
        authSwitchText.innerHTML = 'Remember your password? <a href="#" id="authSwitchLink">Sign In</a>';
        forgotPasswordLink.style.display = 'none';
        
        // Re-attach event listener
        document.getElementById('authSwitchLink').onclick = (e) => {
            e.preventDefault();
            setSignInMode();
        };
    }

    // Event listeners
    closeBtn.onclick = hideModal;
    
    window.onclick = (event) => {
        if (event.target === modal) {
            hideModal();
        }
    };

    authSwitchLink.onclick = (e) => {
        e.preventDefault();
        if (isSignUpMode) {
            setSignInMode();
        } else {
            setSignUpMode();
        }
        hideMessages();
    };

    forgotPasswordLink.onclick = (e) => {
        e.preventDefault();
        setForgotPasswordMode();
        hideMessages();
    };

    // Form submission
    authForm.onsubmit = async (e) => {
        e.preventDefault();
        hideMessages();
        
        const email = emailInput.value.trim();
        const password = passwordInput.value;
        const fullName = fullNameInput.value.trim();
        const confirmPassword = confirmPasswordInput.value;

        // Validation
        if (!email) {
            showError('Email is required');
            return;
        }

        if (isForgotPasswordMode) {
            // Handle password reset
            authSubmit.textContent = 'Sending...';
            authSubmit.disabled = true;
            
            const result = await authManager.resetPassword(email);
            
            if (result.success) {
                showSuccess('Password reset email sent! Check your inbox.');
            } else {
                showError(result.error);
            }
            
            authSubmit.textContent = 'Send Reset Email';
            authSubmit.disabled = false;
            return;
        }

        if (!password) {
            showError('Password is required');
            return;
        }

        if (isSignUpMode) {
            // Sign up validation
            if (!fullName) {
                showError('Full name is required');
                return;
            }
            
            if (password.length < 6) {
                showError('Password must be at least 6 characters');
                return;
            }
            
            if (password !== confirmPassword) {
                showError('Passwords do not match');
                return;
            }

            // Handle sign up
            authSubmit.textContent = 'Creating Account...';
            authSubmit.disabled = true;
            
            const result = await authManager.signUp(email, password, fullName);
            
            if (result.success) {
                showSuccess('Account created! Please check your email to verify your account.');
                setTimeout(() => {
                    hideModal();
                }, 2000);
            } else {
                showError(result.error);
            }
            
            authSubmit.textContent = 'Sign Up';
            authSubmit.disabled = false;
            
        } else {
            // Handle sign in
            authSubmit.textContent = 'Signing In...';
            authSubmit.disabled = true;
            
            const result = await authManager.signIn(email, password);
            
            if (result.success) {
                showSuccess('Successfully signed in!');
                setTimeout(() => {
                    hideModal();
                }, 1000);
            } else {
                showError(result.error);
            }
            
            authSubmit.textContent = 'Sign In';
            authSubmit.disabled = false;
        }
    };

    // Make showModal available globally
    window.showAuthModal = showModal;
});
