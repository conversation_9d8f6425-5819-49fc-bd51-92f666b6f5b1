<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Arabic Learning</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <div class="logo-main">IQA</div>
                <div class="logo-subtitle">Interactive Quranic Arabic</div>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#" class="nav-link">Courses</a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link">About Us</a>
                </li>
            </ul>
        </div>
    </nav>

    <div class="container">
        <div class="background-pattern"></div>
        <div class="content">
            <div class="hero-section">
                <div class="main-text">
                    <h1 class="title">Quranic Arabic <br> your Time, your Pace </h1>
                </div>
                <div class="description-text">
                    <p>IQA is a fully interactive platform designed to teach Quranic Arabic from the ground up. Through engaging lessons, quizzes, flashcards, and practical examples, learners will master grammar (nahw), morphology (sarf), and vocabulary to understand the Qur'an — all at their own pace.</p>
                </div>
            </div>
            <div class="cta-section">
                <button class="auth-button">Sign In / Sign Up</button>
            </div>
        </div>
        <div class="floating-elements">
            <div class="floating-circle circle-1"></div>
            <div class="floating-circle circle-2"></div>
            <div class="floating-circle circle-3"></div>
            <div class="floating-square square-1"></div>
            <div class="floating-square square-2"></div>
        </div>
    </div>

    <!-- Authentication Modal -->
    <div id="authModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">Sign In</h2>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <div id="authError" class="error-message" style="display: none;"></div>
                <div id="authSuccess" class="success-message" style="display: none;"></div>

                <form id="authForm">
                    <div id="nameField" class="form-group" style="display: none;">
                        <label for="fullName">Full Name</label>
                        <input type="text" id="fullName" name="fullName" required>
                    </div>

                    <div class="form-group">
                        <label for="email">Email</label>
                        <input type="email" id="email" name="email" required>
                    </div>

                    <div class="form-group">
                        <label for="password">Password</label>
                        <input type="password" id="password" name="password" required>
                    </div>

                    <div id="confirmPasswordField" class="form-group" style="display: none;">
                        <label for="confirmPassword">Confirm Password</label>
                        <input type="password" id="confirmPassword" name="confirmPassword">
                    </div>

                    <button type="submit" id="authSubmit" class="auth-submit-btn">Sign In</button>
                </form>

                <div class="auth-switch">
                    <p id="authSwitchText">Don't have an account? <a href="#" id="authSwitchLink">Sign Up</a></p>
                </div>

                <div class="forgot-password">
                    <a href="#" id="forgotPasswordLink">Forgot Password?</a>
                </div>
            </div>
        </div>
    </div>

    <script src="js/supabase-client.js"></script>
    <script src="js/auth-modal.js"></script>
</body>
</html>
